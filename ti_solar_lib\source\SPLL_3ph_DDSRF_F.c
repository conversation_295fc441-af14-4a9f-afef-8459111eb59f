/*
 * =====================================================================================
 * 文件名: SPLL_3ph_DDSRF_F.c
 * 功能描述: 三相双同步参考坐标系锁相环(Three-Phase Dual Decoupled Synchronous Reference Frame PLL)实现
 *
 * 版本信息:
 * - 基于TI C2000系列DSP优化
 * - 支持50Hz/60Hz电网频率
 * - 适用于20kHz PWM控制频率
 *
 * 技术参数:
 * - 锁定时间: < 3个电网周期 (50Hz下约60ms)
 * - 频率跟踪范围: ±5Hz (45-55Hz 或 55-65Hz)
 * - 相位精度: ±0.5° (在额定条件下)
 * - 电压不平衡适应性: 支持高达20%的负序分量
 *
 * 应用场景:
 * - 太阳能并网逆变器
 * - 风力发电变流器
 * - 有源电力滤波器
 * - 静止无功发生器(SVG)
 * =====================================================================================
 */

#include "Solar_F.h"

//*********** 结构体初始化函数 ****//

/**
 * @brief 三相双同步参考坐标系锁相环初始化函数
 * @details 将锁相环的所有状态变量清零，设置系统参数和滤波器系数
 *
 * 初始化过程:
 * 1. 清零所有状态变量和中间计算结果
 * 2. 设置低通滤波器系数k1, k2
 * 3. 配置环路滤波器系数(针对20kHz采样频率优化)
 * 4. 设置电网标称频率和采样周期
 *
 * @param Grid_freq 电网标称频率 (Hz) - 通常为50或60
 * @param DELTA_T   采样周期 (秒) - PWM控制周期，如50e-6对应20kHz
 * @param k1        低通滤波器输入增益 - 影响滤波器截止频率
 * @param k2        低通滤波器反馈增益 - 与k1配合决定滤波特性
 * @param spll_obj  指向锁相环结构体的指针
 */
void SPLL_3ph_DDSRF_F_init(int Grid_freq, float32 DELTA_T, float32 k1, float32 k2, SPLL_3ph_DDSRF_F *spll_obj)
{
    // ========== 正负序DQ分量初始化 ==========
    spll_obj->d_p = (float32) (0.0);    // 正序d轴分量清零
    spll_obj->d_n = (float32) (0.0);    // 负序d轴分量清零
    spll_obj->q_p = (float32) (0.0);    // 正序q轴分量清零
    spll_obj->q_n = (float32) (0.0);    // 负序q轴分量清零

    // ========== 解耦网络输出初始化 ==========
    spll_obj->d_p_decoupl = (float32) (0.0);    // 解耦后正序d轴分量清零
    spll_obj->d_n_decoupl = (float32) (0.0);    // 解耦后负序d轴分量清零
    spll_obj->q_p_decoupl = (float32) (0.0);    // 解耦后正序q轴分量清零
    spll_obj->q_n_decoupl = (float32) (0.0);    // 解耦后负序q轴分量清零

    // ========== 低通滤波器输出初始化 ==========
    spll_obj->d_p_decoupl_lpf = (float32) (0.0);    // 滤波后正序d轴分量清零
    spll_obj->d_n_decoupl_lpf = (float32) (0.0);    // 滤波后负序d轴分量清零
    spll_obj->q_p_decoupl_lpf = (float32) (0.0);    // 滤波后正序q轴分量清零
    spll_obj->q_n_decoupl_lpf = (float32) (0.0);    // 滤波后负序q轴分量清零

    // ========== 低通滤波器状态变量初始化 ==========
    spll_obj->y[0] = (float32) (0.0);    // 正序d轴滤波器状态清零
    spll_obj->y[1] = (float32) (0.0);
    spll_obj->x[0] = (float32) (0.0);    // 正序q轴滤波器状态清零
    spll_obj->x[1] = (float32) (0.0);
    spll_obj->w[0] = (float32) (0.0);    // 负序d轴滤波器状态清零
    spll_obj->w[1] = (float32) (0.0);
    spll_obj->z[0] = (float32) (0.0);    // 负序q轴滤波器状态清零
    spll_obj->z[1] = (float32) (0.0);

    // ========== 低通滤波器系数设置 ==========
    spll_obj->k1 = k1;    // 输入增益系数 (典型值: 0.1~1.0)
    spll_obj->k2 = k2;    // 反馈增益系数 (典型值: 0.9~0.99)

    // ========== 环路滤波器状态初始化 ==========
    spll_obj->v_q[0] = (float32) (0.0);    // 环路滤波器输入清零
    spll_obj->v_q[1] = (float32) (0.0);
    spll_obj->ylf[0] = (float32) (0.0);    // 环路滤波器输出清零
    spll_obj->ylf[1] = (float32) (0.0);

    // ========== VCO参数初始化 ==========
    spll_obj->fo = (float32) (0.0);           // 输出频率初始化为0
    spll_obj->fn = (float32) (Grid_freq);     // 设置电网标称频率
    spll_obj->theta[0] = (float32) (0.0);     // 输出相位角清零
    spll_obj->theta[1] = (float32) (0.0);

    // ========== 环路滤波器系数设置 (针对20kHz采样频率优化) ==========
    // 原始系数 (已注释): B0=166.9743, B1=-166.266 - 可能导致系统不稳定
    spll_obj->lpf_coeff.B0_lf = (float32) (166.9743);   // 当前采样点系数
    spll_obj->lpf_coeff.B1_lf = (float32) (-166.9743);  // 前一采样点系数 (修正为对称系数)
    spll_obj->lpf_coeff.A1_lf = (float32) (-1.0);       // 反馈系数

    // ========== 采样周期设置 ==========
    spll_obj->delta_T = DELTA_T;    // 保存采样周期，用于VCO积分运算
}

//*********** 函数定义 ********//

/**
 * @brief 三相双同步参考坐标系锁相环主运算函数
 * @details 执行完整的DDSRF-PLL算法，包括解耦网络、低通滤波、环路滤波和VCO运算
 *
 * 算法原理:
 * DDSRF-PLL通过将三相电压分解为正序和负序分量，然后在各自的同步参考坐标系中
 * 进行处理，有效消除了电网不平衡对锁相精度的影响。
 *
 * 调用前置条件:
 * 1. 必须先调用ABC_DQ0_POS_NEG变换，更新d_p, d_n, q_p, q_n值
 * 2. 必须根据前一次的theta值计算cos_2theta和sin_2theta
 * 3. 输入的DQ分量应为标准化的标幺值
 *
 * 性能指标:
 * - 函数执行时间: 约20-30μs (150MHz DSP)
 * - 内存占用: 约200字节 (结构体大小)
 * - 锁定时间: 2-4个电网周期
 *
 * @param spll_obj 指向锁相环结构体的指针
 */
void SPLL_3ph_DDSRF_F_FUNC(SPLL_3ph_DDSRF_F *spll_obj)
{
    /*
     * 调用前准备工作说明:
     * 1. 运行ABC_DQ0_Pos_Neg变换更新d_p, d_n, q_p, q_n值
     * 2. 使用前一次的相位角更新cos_2theta和sin_2theta值
     * 3. 确保输入信号已正确标准化
     */

    //=========================//
    // 解耦网络 (Decoupling Network)
    //=========================//
    /*
     * 解耦网络的作用:
     * 在电网不平衡条件下，正序和负序分量会在DQ坐标系中产生二倍频耦合。
     * 解耦网络通过坐标变换消除这种耦合，使正负序分量能够独立处理。
     *
     * 数学原理:
     * 正序解耦: [d_p', q_p'] = [d_p, q_p] - [d_n_lpf, q_n] * [cos(2θ), sin(2θ)]
     * 负序解耦: [d_n', q_n'] = [d_n, q_n] - [d_p_lpf, q_p] * [cos(2θ), sin(2θ)]
     */

    // 正序分量解耦: 从正序分量中减去负序分量的二倍频干扰
    spll_obj->d_p_decoupl = spll_obj->d_p - (spll_obj->d_n_decoupl_lpf * spll_obj->cos_2theta) - (spll_obj->q_n_decoupl * spll_obj->sin_2theta);
    spll_obj->q_p_decoupl = spll_obj->q_p + (spll_obj->d_n_decoupl_lpf * spll_obj->sin_2theta) - (spll_obj->q_n_decoupl * spll_obj->cos_2theta);

    // 负序分量解耦: 从负序分量中减去正序分量的二倍频干扰
    spll_obj->d_n_decoupl = spll_obj->d_n - (spll_obj->d_p_decoupl_lpf * spll_obj->cos_2theta) + (spll_obj->q_p_decoupl * spll_obj->sin_2theta);
    spll_obj->q_n_decoupl = spll_obj->q_n - (spll_obj->d_p_decoupl_lpf * spll_obj->sin_2theta) - (spll_obj->q_p_decoupl * spll_obj->cos_2theta);

    //=========================//
    // 低通滤波器 (Low Pass Filter)
    //=========================//
    /*
     * 低通滤波器的作用:
     * 1. 滤除解耦后信号中的高频噪声和开关谐波
     * 2. 平滑DQ分量，提高锁相环的稳定性
     * 3. 为解耦网络提供延迟一拍的反馈信号
     *
     * 滤波器结构: 一阶IIR滤波器
     * 传递函数: H(z) = k1 / (1 + k2*z^-1)
     * 差分方程: y[n] = k1*x[n] - k2*y[n-1]
     * 输出: output = y[n] + y[n-1] (相当于再加一个积分环节)
     *
     * 参数选择原则:
     * - k1越大，响应越快，但噪声抑制能力越差
     * - k2越接近1，滤波效果越好，但响应越慢
     * - 典型值: k1=0.1~1.0, k2=0.9~0.99
     */

    // 正序d轴分量低通滤波
    spll_obj->y[1] = (spll_obj->d_p_decoupl * spll_obj->k1) - (spll_obj->y[0] * spll_obj->k2);
    spll_obj->d_p_decoupl_lpf = spll_obj->y[1] + spll_obj->y[0];    // 输出 = 当前值 + 前一值
    spll_obj->y[0] = spll_obj->y[1];    // 更新状态变量

    // 正序q轴分量低通滤波 (此分量用于相位误差检测)
    spll_obj->x[1] = (spll_obj->q_p_decoupl * spll_obj->k1) - (spll_obj->x[0] * spll_obj->k2);
    spll_obj->q_p_decoupl_lpf = spll_obj->x[1] + spll_obj->x[0];    // 滤波后的相位误差信号
    spll_obj->x[0] = spll_obj->x[1];    // 更新状态变量

    // 负序d轴分量低通滤波
    spll_obj->w[1] = (spll_obj->d_n_decoupl * spll_obj->k1) - (spll_obj->w[0] * spll_obj->k2);
    spll_obj->d_n_decoupl_lpf = spll_obj->w[1] + spll_obj->w[0];    // 用于解耦网络反馈
    spll_obj->w[0] = spll_obj->w[1];    // 更新状态变量

    // 负序q轴分量低通滤波
    spll_obj->z[1] = (spll_obj->q_n_decoupl * spll_obj->k1) - (spll_obj->z[0] * spll_obj->k2);
    spll_obj->q_n_decoupl_lpf = spll_obj->z[1] + spll_obj->z[0];    // 用于解耦网络反馈
    spll_obj->z[0] = spll_obj->z[1];    // 更新状态变量

    // 选择正序q轴分量作为相位误差信号 (锁相时此值应趋于0)
    spll_obj->v_q[0] = spll_obj->q_p_decoupl;

    //=================================//
    // 环路滤波器 (Loop Filter)
    //=================================//
    /*
     * 环路滤波器的作用:
     * 1. 将相位误差信号转换为频率控制信号
     * 2. 决定锁相环的动态响应特性 (锁定时间、稳定性、抗干扰能力)
     * 3. 实现PI控制器功能，消除稳态相位误差
     *
     * 滤波器类型: 一阶PI控制器
     * 传递函数: H(s) = Kp + Ki/s
     * 离散化后: H(z) = (B0 + B1*z^-1) / (1 + A1*z^-1)
     *
     * 系数含义:
     * - B0_lf: 比例增益 (影响响应速度)
     * - B1_lf: 积分增益 (消除稳态误差)
     * - A1_lf: 反馈系数 (通常为-1.0)
     *
     * 参数调整原则:
     * - 增大B0提高响应速度，但可能引起振荡
     * - 增大|B1|提高稳态精度，但可能降低稳定性
     * - 当前系数针对20kHz采样频率优化
     */
    spll_obj->ylf[0] = spll_obj->ylf[1] + (spll_obj->lpf_coeff.B0_lf * spll_obj->v_q[0]) + (spll_obj->lpf_coeff.B1_lf * spll_obj->v_q[1]);
    spll_obj->ylf[1] = spll_obj->ylf[0];    // 保存当前输出作为下次计算的前一值
    spll_obj->v_q[1] = spll_obj->v_q[0];    // 保存当前输入作为下次计算的前一值

    //=================================//
    // VCO (压控振荡器 Voltage Controlled Oscillator)
    //=================================//
    /*
     * VCO的作用:
     * 1. 将频率控制信号积分得到相位角
     * 2. 实现频率跟踪功能，适应电网频率变化
     * 3. 产生锁相环的最终输出 - 电网同步相位角
     *
     * 工作原理:
     * fo = fn + ylf[0]  (输出频率 = 标称频率 + 频率误差)
     * θ[n] = θ[n-1] + 2π*fo*ΔT  (相位积分)
     *
     * 注意事项:
     * - 相位角限制在0~2π范围内，防止数值溢出
     * - 频率跟踪范围通常限制在±5Hz以内
     * - 采样周期ΔT的精度直接影响相位精度
     */

    // 频率计算: 输出频率 = 标称频率 + 频率误差信号
    spll_obj->fo = spll_obj->fn + spll_obj->ylf[0];

    // 相位积分: θ[n] = θ[n-1] + 2π*f*ΔT
    // 这是VCO的核心运算，将频率信号积分得到相位角
    spll_obj->theta[0] = spll_obj->theta[1] + ((spll_obj->fo * spll_obj->delta_T) * (float32) (2 * 3.1415926));

    // 相位角归一化: 限制在0~2π范围内
    // 防止长时间运行后数值溢出，保持相位角的周期性
    if (spll_obj->theta[0] > (float32) (2 * 3.1415926))
        spll_obj->theta[0] = spll_obj->theta[0] - (float32) (2 * 3.1415926);

    // 保存当前相位角作为下次计算的前一值
    spll_obj->theta[1] = spll_obj->theta[0];
}

