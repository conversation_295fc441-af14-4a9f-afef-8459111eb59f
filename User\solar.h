#ifndef __SOLAR_H
#define __SOLAR_H

#include "common.h"

#include <Sample.h>

// #include "AdamLib.h"
#include "SCI.h"
#include "protect.h"
#include "pvboost.h"

#include "comm.h"
#include "dq_svpwm.h"
#include "cntl.h"

#define SOLAR_DEBUG 1

#if SOLAR_DEBUG
typedef struct debug_handle_t
{
    float cntl_value;

    float data_buff[20][100];

    float dlog_buff[4][100];
    float dlog_get[4];
    DLOG_4CH_F hdlog1;

    // 电网模拟
    DQ0_ABC_F simulation_grid;
    DQ0_ABC_F simulation_current; // 电流
    float simulation_theta;
    float simulation_freq;
    int simulation_sw;

    uint32_t TIM2_time_count;
    uint32_t TIM0_time_count;
    float time2_count, time2_count_max;
    float time0_count, time0_count_max;
} debug_handle_t;
extern debug_handle_t hdebug;
#endif

typedef struct _solar_handle // 太阳能
{
    float meter_Ia, meter_Ib, meter_Ic, meter_LC;
    float Ia_gain, Ib_gain, Ic_gain, LC_gain;

    float Out_d;
    float Out_q;
    dq_svpwm_t hDQ_SVPWM;

    ABC_DQ0_POS_F hABC_DQ0_POS;
    ABC_DQ0_NEG_F hABC_DQ0_NEG;
    DQ0_ABC_F hV_DQ0_ABC;
    SPLL_3ph_DDSRF_F hSPLL_3ph_DDSRF;
    float spll_theta;              // [0,2PI]
    float spll_freq;               // [Hz]
    float spll_theta_compensation; // 相位补偿
    int spll_phase_sequence;       // 相序 1:pos, 0:neg

    ABC_DQ0_POS_F hIabc_dq0_pos;
    ABC_DQ0_NEG_F hIabc_dq0_neg;
    SPLL_3ph_DDSRF_F hIspll_3ph_ddsrf;
    float Id_lpf;
    float Iq_lpf;

    // 有功功率计算公式 P = Vd * Id + Vq * Iq
    //    activePower = vd * id + vq * iq;
    // 无功功率计算公式 Q = Vq * Id - Vd * Iq
    //    reactivePower = vq * id - vd * iq;

    MPPT_PNO_F hMPPT_PNO1;
    MPPT_PNO_F hMPPT_PNO2;
    MPPT_INCC_F hMPPT_INCC1;
    MPPT_INCC_F hMPPT_INCC2;
    MPPT_INCC_I_F hMPPT_INCC_I_1;
    MPPT_INCC_I_F hMPPT_INCC_I_2;
    float Vpvref_mpptOut1;
    float Vpvref_mpptOut2;

    CNTL_PI_F hCNTL_PI_Vpv1;
    CNTL_PI_F hCNTL_PI_Vpv2;
    CNTL_PI_F hCNTL_PI_Vbus;
    CNTL_PI_F hCNTL_PI_BusBalance;

    CNTL_PI_F hCNTL_PI_d;
    CNTL_PI_F hCNTL_PI_q;

    int16_t cntl_bus_balance_compensation; //(1,-1)
    int16_t cntl_bus_balance_table[400];

    int is_output_epwm_enable;
    //    int is_interconnection; // 并网
    int is_amp_sync_ok; // 幅值已经同步 // 并网条件
    int Ctrl_Power_SW;

    Protect_Handle_t hProtect;
    PVBoost_Handle_t hBoost;

    Sample_AC_HandleTypeDef Vgrid_a;
    Sample_AC_HandleTypeDef Vgrid_b;
    Sample_AC_HandleTypeDef Vgrid_c;
    Sample_AC_HandleTypeDef Vinv_a;
    Sample_AC_HandleTypeDef Vinv_b;
    Sample_AC_HandleTypeDef Vinv_c;
    Sample_AC_HandleTypeDef Ia;
    Sample_AC_HandleTypeDef Ib;
    Sample_AC_HandleTypeDef Ic;

    Sample_DC_HandleTypeDef Ipv1;
    Sample_DC_HandleTypeDef Ipv2;
    Sample_DC_HandleTypeDef Vpv1;
    Sample_DC_HandleTypeDef Vpv2;
    Sample_DC_HandleTypeDef Vbus_pos;
    Sample_DC_HandleTypeDef Vbus_neg;

    float Vbus;
    float Vpv1_lpf;
    float Vpv2_lpf;
    float Ipv1_lpf;
    float Ipv2_lpf;

    float Input_Power_MPPT1; // 太阳能板功率
    float Input_Power_MPPT2; // 太阳能板功率
    float Input_Power_Total; // 太阳能板功率

    float Output_Power_a_rms_xp;     // 逆变功率
    float Output_Power_b_rms_xp;     // 逆变功率
    float Output_Power_c_rms_xp;     // 逆变功率
    float Output_Power_Total_rms_xp; // 逆变功率

    Protect_Member_t Protect_Ia;
    Protect_Member_t Protect_Ib;
    Protect_Member_t Protect_Ic;

    Protect_Member_t Protect_Ipv1;
    Protect_Member_t Protect_Ipv2;

    Protect_Member_t Protect_Ia_II;
    Protect_Member_t Protect_Ib_II;
    Protect_Member_t Protect_Ic_II;

    Protect_Member_t Protect_Ipv1_II;
    Protect_Member_t Protect_Ipv2_II;

    Protect_Member_t Protect_Grid_Freq;
    Protect_Member_t Protect_Grid_Ua;
    Protect_Member_t Protect_Grid_Ub;
    Protect_Member_t Protect_Grid_Uc;

    Protect_Member_t Protect_Vpv1;
    Protect_Member_t Protect_Vpv2;

    Protect_Member_t Protect_VBusBalance;
    Protect_Member_t Protect_VBusSum;

    const char *message_list[10]; // 消息队列
    uint16_t message_head;        // 消息头索引
    uint16_t message_count;       // 消息计数器

    enum _solar_state
    {
        HSTATE_STOP,      // 停机状态
        HSTATE_START,     // 开始状态
        HSTATE_SYNC,      // 同步状态
        HSTATE_MERGE_ING, // 并网就绪
        HSTATE_RUNING,    // 并网运行
    } state;              // 太阳能状态

    struct solar_data_t
    {
        uint32_t data_head;
        uint32_t solar_state;

        float Total_EG;
        float Today_EG;
        float active_power;   // 有功功率
        float apparent_power; // 视在功率
        float Pf;

        float Vpv1;
        float Ipv1;
        float Vpv2;
        float Ipv2;
        float VBus;
        float VBusPos;
        float VBusNeg;

        float GVa;
        float GVb;
        float GVc;

        float Ia;
        float Ib;
        float Ic;

        float Freq;

        uint32_t boost1_pwm;
        uint32_t boost2_pwm;

        float Vpvref_mpptOut1;
        float Vpvref_mpptOut2;

        float Input_Power_MPPT1; // 太阳能板功率
        float Input_Power_MPPT2; // 太阳能板功率
        float Input_Power_Total; // 太阳能板功率

        float Output_Power_a; // 逆变功率
        float Output_Power_b; // 逆变功率
        float Output_Power_c; // 逆变功率
        float Output_Power_T; // 逆变功率

        float tim_program_elapsed_time; // 程序占用时间
        uint32_t protect_word[2];
    } data;

    uint32_t time_tick;

} solar_handle_t;

extern solar_handle_t h;
// extern solar_handle_t *hsolar;

#define SOLAR_GET_TICK() (h.time_tick)
#define SOLAR_GET_TICK_COUNT(start_tick) (h.time_tick - start_tick)

void solar_ISR_1ms(void);
void solar_ISR_50us(void);
void solar_Loop(void);
void solar_Init(void);

void solar_Sampler(void);
void solar_AC_RMS_Calc(void);

void solar_Protector_us(void);
void solar_Protector_ms(void);
void solar_PV_Manager(void);
void solar_StateManager(void);
void solar_Communicator(void);

void solar_amp_sync(PVBoost_Handle_t *PVB); // 电网电压追踪 (power grid voltage trace)
void solar_MPPT(void);                      // 最大功率点追踪 (max power point trace)

void solar_cntl_init(void);
void solar_cntl_calc(void);

void solar_mppt_init(void);
void solar_mppt_calc(void);

void solar_spll_init(void);
void solar_spll_calc(void);

void solar_output_epwm(void);

void solar_off(void);
void solar_boost_enable(void);
void solar_boost_disable(void);
void solar_inv_pwm_enable(void);
void solar_inv_pwm_disable(void);

#define SOLAR_GET_INTERCONNECTION() (GpioDataRegs.GPCDAT.bit.GPIO72)

#if SOLAR_DEBUG
void solar_debug_init(void);
#endif

#endif
